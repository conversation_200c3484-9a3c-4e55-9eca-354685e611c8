#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/app/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/build/node_modules:/app/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/node_modules:/app/node_modules/.pnpm/prisma@5.22.0/node_modules:/app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/app/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/build/node_modules:/app/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/node_modules:/app/node_modules/.pnpm/prisma@5.22.0/node_modules:/app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../build/index.js" "$@"
else
  exec node  "$basedir/../../build/index.js" "$@"
fi
